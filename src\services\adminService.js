import axios from 'axios';
import { API_CONFIG } from '../config/api';

// Admin API Service - Terpisah dari user authentication
class AdminService {
  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
    this.adminToken = localStorage.getItem('adminToken');
    this.adminUser = JSON.parse(localStorage.getItem('adminUser') || '{}');
  }

  // Helper method untuk authenticated requests
  async adminApiRequest(endpoint, options = {}) {
    const token = localStorage.getItem('adminToken');
    
    const config = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers,
      },
      ...options,
    };
    
    try {
      const response = await axios(endpoint, config);
      console.log('Admin API Response:', response.data); // Debug log
      return response.data;
    } catch (error) {
      console.error('Admin API Error:', error); // Debug log
      if (error.response?.status === 401) {
        // Token expired, clear admin session
        this.logout();
        throw new Error('Session expired. Please login again.');
      }
      
      const errorData = error.response?.data;
      throw new Error(errorData?.error?.message || error.message || 'Request failed');
    }
  }

  // Admin Authentication Methods
  async login(username, password) {
    try {
      const response = await axios.post('/admin/login', {
        username,
        password
      });
      
      if (response.data.success) {
        const { admin, token } = response.data.data;
        
        // Store admin session
        localStorage.setItem('adminToken', token);
        localStorage.setItem('adminUser', JSON.stringify(admin));
        
        this.adminToken = token;
        this.adminUser = admin;
        
        return { admin, token };
      } else {
        throw new Error(response.data.error?.message || 'Login failed');
      }
    } catch (error) {
      const errorData = error.response?.data;
      throw new Error(errorData?.error?.message || error.message || 'Login failed');
    }
  }

  async logout() {
    try {
      if (this.adminToken) {
        await this.adminApiRequest('/admin/logout', { method: 'POST' });
      }
    } catch (error) {
      console.warn('Logout request failed:', error.message);
    } finally {
      // Clear admin session regardless of API response
      localStorage.removeItem('adminToken');
      localStorage.removeItem('adminUser');
      this.adminToken = null;
      this.adminUser = {};
    }
  }

  async getProfile() {
    return await this.adminApiRequest('/admin/profile');
  }

  async updateProfile(profileData) {
    return await this.adminApiRequest('/admin/profile', {
      method: 'PUT',
      data: profileData
    });
  }

  async changePassword(currentPassword, newPassword) {
    return await this.adminApiRequest('/admin/change-password', {
      method: 'POST',
      data: {
        currentPassword,
        newPassword
      }
    });
  }

  async registerAdmin(adminData) {
    return await this.adminApiRequest('/admin/register', {
      method: 'POST',
      data: adminData
    });
  }

  // User Management Methods
  async getUsers(page = 1, limit = 10, search = '', sortBy = 'created_at', sortOrder = 'DESC') {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      search,
      sortBy,
      sortOrder
    });

    try {
      const response = await this.adminApiRequest(`/admin/users?${params}`);

      // Ensure response has the expected structure
      if (!response.data) {
        response.data = {};
      }
      if (!Array.isArray(response.data.users)) {
        response.data.users = [];
      }
      if (!response.data.pagination) {
        response.data.pagination = {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false
        };
      }

      return response;
    } catch (error) {
      // Return safe default structure on error
      return {
        success: false,
        data: {
          users: [],
          pagination: {
            page: 1,
            limit: 10,
            total: 0,
            totalPages: 0,
            hasNext: false,
            hasPrev: false
          }
        }
      };
    }
  }

  async getUserById(userId) {
    return await this.adminApiRequest(`/admin/users/${userId}`);
  }

  async updateUserTokenBalance(userId, tokenBalance, action = 'set') {
    return await this.adminApiRequest(`/admin/users/${userId}/token-balance`, {
      method: 'PUT',
      data: { token_balance: tokenBalance, action }
    });
  }

  async deleteUser(userId) {
    return await this.adminApiRequest(`/admin/users/${userId}`, {
      method: 'DELETE'
    });
  }

  // Utility Methods
  isAuthenticated() {
    return !!this.adminToken && !!localStorage.getItem('adminToken');
  }

  getCurrentAdmin() {
    return this.adminUser;
  }

  hasRole(requiredRole) {
    const roleHierarchy = {
      'moderator': 1,
      'admin': 2,
      'superadmin': 3
    };
    
    const userLevel = roleHierarchy[this.adminUser.role] || 0;
    const requiredLevel = roleHierarchy[requiredRole] || 0;
    
    return userLevel >= requiredLevel;
  }

  // Error handling helper
  handleError(error) {
    console.error('Admin API Error:', error);
    
    const errorMessages = {
      'UNAUTHORIZED': 'Session expired. Please login again.',
      'FORBIDDEN': 'You do not have permission to perform this action.',
      'VALIDATION_ERROR': 'Please check your input and try again.',
      'NOT_FOUND': 'The requested resource was not found.',
      'SERVICE_UNAVAILABLE': 'Service is temporarily unavailable. Please try again later.',
    };
    
    return errorMessages[error.code] || error.message || 'An unexpected error occurred.';
  }
}

// Export singleton instance
export default new AdminService();
